"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { signOut, useSession } from "next-auth/react";

const links = [
  { href: "/", label: "Dashboard" },
  { href: "/characters", label: "Characters" },
];

export default function AppHeader() {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const router = useRouter();

  const isAdmin = session?.user.role === "ADMIN";

  const navigation = isAdmin
    ? [...links, { href: "/llm-models", label: "LLM Providers" }]
    : links;

  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push("/login");
  };

  return (
    <header className="border-b border-neutral-200 bg-white">
      <div className="mx-auto flex w-full max-w-6xl items-center justify-between gap-4 px-6 py-4">
        <nav className="flex items-center gap-4 text-sm font-medium text-neutral-600">
          {navigation.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                href={item.href}
                className={
                  isActive
                    ? "text-neutral-900"
                    : "transition hover:text-neutral-900"
                }
              >
                {item.label}
              </Link>
            );
          })}
        </nav>
        <div className="flex items-center gap-3 text-sm text-neutral-600">
          {status === "authenticated" && session?.user ? (
            <>
              <span>
                {session.user.name ?? session.user.email ?? "Signed in"} · {session.user.role}
              </span>
              <button
                type="button"
                onClick={handleSignOut}
                className="rounded border border-neutral-300 px-3 py-1 text-xs font-semibold text-neutral-700 transition hover:border-neutral-400 hover:text-neutral-900"
              >
                Sign out
              </button>
            </>
          ) : (
            <div className="flex items-center gap-2">
              <Link
                href="/login"
                className="rounded border border-neutral-300 px-3 py-1 text-xs font-semibold text-neutral-700 transition hover:border-neutral-400 hover:text-neutral-900"
              >
                Log in
              </Link>
              <Link
                href="/register"
                className="rounded bg-neutral-900 px-3 py-1 text-xs font-semibold text-white transition hover:bg-neutral-800"
              >
                Register
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
