import type { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";

export type CharacterAccessOptions = {
  slug: string;
  userId: string;
  isAdmin: boolean;
  include?: Prisma.CharacterInclude;
  select?: Prisma.CharacterSelect;
};

export type CharacterReadOptions = {
  slug: string;
  include?: Prisma.CharacterInclude;
  select?: Prisma.CharacterSelect;
};

/**
 * Get character for read access (viewing details) - available to all authenticated users
 */
export async function getCharacterForRead<T extends CharacterReadOptions>({
  slug,
  include,
  select,
}: T) {
  const query: any = { where: { slug } };
  if (include) query.include = include;
  if (select) query.select = select;

  return prisma.character.findUnique(query);
}

/**
 * Authorize character access for write operations (editing, deleting, chatting) - restricted to owners/admins
 */
export async function authorizeCharacterAccess<T extends CharacterAccessOptions>({
  slug,
  userId,
  isAdmin,
  include,
  select,
}: T) {
  const query: any = { where: isAdmin ? { slug } : { slug, ownerId: userId } };
  if (include) query.include = include;
  if (select) query.select = select;

  if (isAdmin) {
    return prisma.character.findUnique(query);
  }

  return prisma.character.findFirst(query);
}
