import Link from "next/link";
import { redirect } from "next/navigation";

import LoginForm from "./LoginForm";
import { auth } from "../../../../auth";

export default async function LoginPage() {
  const session = await auth();

  if (session?.user) {
    redirect("/");
  }

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-md flex-col justify-center gap-6 px-6 py-16">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold text-neutral-900">Welcome back</h1>
        <p className="text-sm text-neutral-600">
          Sign in to continue managing characters and chat providers.
        </p>
      </div>
      <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
        <LoginForm />
        <p className="mt-4 text-center text-xs text-neutral-600">
          No account? <Link href="/register" className="font-medium text-neutral-900">Register here</Link>.
        </p>
      </div>
    </main>
  );
}
