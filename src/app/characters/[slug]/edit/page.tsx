import Link from "next/link";
import { notFound, redirect } from "next/navigation";

import EditCharacterForm from "./EditCharacterForm";
import { authorizeCharacterAccess } from "@/lib/permissions";
import { auth } from "../../../../../auth";

type RouteParams = {
  params: Promise<{
    slug: string;
  }>;
};

export default async function CharacterEditPage({ params }: RouteParams) {
  const { slug } = await params;

  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  const character = await authorizeCharacterAccess({
    slug,
    userId: session.user.id,
    isAdmin: session.user.role === "ADMIN",
    select: {
      slug: true,
      name: true,
      description: true,
      personality: true,
      scenario: true,
      creatorNotes: true,
      firstMessage: true,
      tags: true,
      alternateGreetings: true,
      groupOnlyGreetings: true,
      avatarUrl: true,
      ownerId: true,
    },
  });

  if (!character) {
    notFound();
  }

  const canManage =
    session.user.role === "ADMIN" || character.ownerId === session.user.id;

  if (!canManage) {
    notFound();
  }

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-3xl flex-col gap-6 px-6 py-12">
      <div className="space-y-2">
        <Link
          href={`/characters/${character.slug}`}
          className="text-sm font-medium text-neutral-600 transition hover:text-neutral-900"
        >
          ← Back to Character
        </Link>
        <h1 className="text-3xl font-semibold text-neutral-900">Edit Character</h1>
        <p className="text-sm text-neutral-600">
          Update persona details, greetings, and metadata for this character.
        </p>
      </div>

      <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
        <EditCharacterForm
          character={{
            slug: character.slug,
            name: character.name,
            description: character.description,
            personality: character.personality,
            scenario: character.scenario,
            creatorNotes: character.creatorNotes,
            firstMessage: character.firstMessage,
            tags: character.tags,
            alternateGreetings: character.alternateGreetings,
            groupOnlyGreetings: character.groupOnlyGreetings,
            avatarUrl: character.avatarUrl,
          }}
        />
      </div>
    </main>
  );
}
