"use client";

import { useState, FormEvent } from "react";
import { useRouter } from "next/navigation";

type EditableCharacter = {
  slug: string;
  name: string;
  description: string | null;
  personality: string | null;
  scenario: string | null;
  creatorNotes: string | null;
  firstMessage: string | null;
  tags: string[];
  alternateGreetings: string[];
  groupOnlyGreetings: string[];
  avatarUrl: string | null;
};

const listToText = (list: string[]) => list.join("\n");

const textToList = (value: string) =>
  value
    .split("\n")
    .map((entry) => entry.trim())
    .filter((entry) => entry.length > 0);

export default function EditCharacterForm({ character }: { character: EditableCharacter }) {
  const router = useRouter();
  const [name, setName] = useState(character.name);
  const [avatarUrl, setAvatarUrl] = useState(character.avatarUrl ?? "");
  const [description, setDescription] = useState(character.description ?? "");
  const [personality, setPersonality] = useState(character.personality ?? "");
  const [scenario, setScenario] = useState(character.scenario ?? "");
  const [creatorNotes, setCreatorNotes] = useState(character.creatorNotes ?? "");
  const [firstMessage, setFirstMessage] = useState(character.firstMessage ?? "");
  const [tags, setTags] = useState(character.tags.join(", "));
  const [alternateGreetings, setAlternateGreetings] = useState(listToText(character.alternateGreetings));
  const [groupOnlyGreetings, setGroupOnlyGreetings] = useState(listToText(character.groupOnlyGreetings));
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const onSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    const payload = {
      name,
      avatarUrl: avatarUrl.trim().length ? avatarUrl.trim() : null,
      description,
      personality,
      scenario,
      creatorNotes,
      firstMessage,
      tags: tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0),
      alternateGreetings: textToList(alternateGreetings),
      groupOnlyGreetings: textToList(groupOnlyGreetings),
    };

    const response = await fetch(`/api/characters/${character.slug}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const body = (await response.json().catch(() => null)) as { error?: string } | null;
      setError(body?.error ?? "Failed to update character.");
      setLoading(false);
      return;
    }

    setSuccess("Character updated.");
    setLoading(false);
    router.push(`/characters/${character.slug}`);
    router.refresh();
  };

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Name
          <input
            type="text"
            value={name}
            onChange={(event) => setName(event.currentTarget.value)}
            required
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Avatar URL
          <input
            type="url"
            value={avatarUrl}
            onChange={(event) => setAvatarUrl(event.currentTarget.value)}
            placeholder="https://example.com/avatar.png"
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Description
          <textarea
            value={description}
            onChange={(event) => setDescription(event.currentTarget.value)}
            rows={4}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Personality
          <textarea
            value={personality}
            onChange={(event) => setPersonality(event.currentTarget.value)}
            rows={4}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Scenario
          <textarea
            value={scenario}
            onChange={(event) => setScenario(event.currentTarget.value)}
            rows={4}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Creator Notes
          <textarea
            value={creatorNotes}
            onChange={(event) => setCreatorNotes(event.currentTarget.value)}
            rows={4}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
      </div>

      <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
        First Message
        <textarea
          value={firstMessage}
          onChange={(event) => setFirstMessage(event.currentTarget.value)}
          rows={3}
          className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
        />
      </label>

      <div className="grid gap-6 md:grid-cols-2">
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Tags (comma separated)
          <input
            type="text"
            value={tags}
            onChange={(event) => setTags(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
          Alternate Greetings (one per line)
          <textarea
            value={alternateGreetings}
            onChange={(event) => setAlternateGreetings(event.currentTarget.value)}
            rows={4}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
          />
        </label>
      </div>

      <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
        Group-only Greetings (one per line)
        <textarea
          value={groupOnlyGreetings}
          onChange={(event) => setGroupOnlyGreetings(event.currentTarget.value)}
          rows={4}
          className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
        />
      </label>

      {error ? <p className="text-sm text-red-600">{error}</p> : null}
      {success ? <p className="text-sm text-green-600">{success}</p> : null}

      <button
        type="submit"
        disabled={loading}
        className="inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white transition disabled:opacity-60"
      >
        {loading ? "Saving…" : "Save Changes"}
      </button>
    </form>
  );
}
