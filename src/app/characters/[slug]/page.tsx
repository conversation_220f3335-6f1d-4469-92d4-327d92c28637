import Link from "next/link";
import { notFound, redirect } from "next/navigation";

import DeleteCharacterButton from "@/components/characters/DeleteCharacterButton";
import type { CharacterBookEntry } from "@/generated/prisma";
import { getCharacterForRead } from "@/lib/permissions";
import { auth } from "../../../../auth";

const toIsoString = (date: Date) => date.toISOString();

const isRecord = (value: unknown): value is Record<string, unknown> =>
  typeof value === "object" && value !== null;

const bookTitle = (entries: CharacterBookEntry[], raw: unknown) => {
  if (entries.length === 0) {
    return null;
  }

  if (isRecord(raw) && typeof raw.name === "string" && raw.name.trim()) {
    return raw.name.trim();
  }

  return "Character Lore";
};

type CharacterPageParams = {
  params: Promise<{
    slug: string;
  }>;
};

export default async function CharacterDetailPage({ params }: CharacterPageParams) {
  const { slug } = await params;

  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  const character = await getCharacterForRead({
    slug,
    include: {
      bookEntries: {
        orderBy: {
          insertionOrder: "asc",
        },
      },
    },
  });

  if (!character) {
    notFound();
  }

  const sortedBookEntries = [...character.bookEntries].sort((a, b) => {
    const left = a.insertionOrder ?? a.entryId;
    const right = b.insertionOrder ?? b.entryId;
    return left - right;
  });

  const bookName = bookTitle(sortedBookEntries, character.characterBook);
  const isAdmin = session.user.role === "ADMIN";
  const canManage = isAdmin || character.ownerId === session.user.id;

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-5xl flex-col gap-10 px-6 py-12">
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="space-y-2">
          <Link
            href="/characters"
            className="text-sm font-medium text-neutral-600 transition hover:text-neutral-900"
          >
            ← Back to Characters
          </Link>
          <div>
            <h1 className="text-3xl font-semibold text-neutral-900">{character.name}</h1>
            <p className="text-sm text-neutral-500">
              Version {character.characterVersion} · Created {toIsoString(character.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex flex-wrap items-center justify-end gap-3">
          <Link
            href={`/characters/${character.slug}/chat`}
            className="inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white"
          >
            Open Chat
          </Link>
          {canManage ? (
            <>
              <Link
                href={`/characters/${character.slug}/edit`}
                className="inline-flex items-center justify-center rounded border border-neutral-300 px-4 py-2 text-sm font-semibold text-neutral-700 transition hover:border-neutral-400 hover:text-neutral-900"
              >
                Edit
              </Link>
              <DeleteCharacterButton slug={character.slug} />
            </>
          ) : null}
        </div>
      </div>

      <section className="grid gap-6 lg:grid-cols-[minmax(0,1.5fr)_minmax(0,1fr)]">
        <div className="space-y-6">
          <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
            <h2 className="text-lg font-semibold text-neutral-900">Profile</h2>
            <dl className="mt-4 space-y-3 text-sm text-neutral-700">
              <div>
                <dt className="font-medium text-neutral-600">Description</dt>
                <dd className="mt-1 whitespace-pre-wrap">
                  {character.description || "—"}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-neutral-600">Scenario</dt>
                <dd className="mt-1 whitespace-pre-wrap">
                  {character.scenario || "—"}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-neutral-600">Personality</dt>
                <dd className="mt-1 whitespace-pre-wrap">
                  {character.personality || "—"}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-neutral-600">Creator Notes</dt>
                <dd className="mt-1 whitespace-pre-wrap">
                  {character.creatorNotes || "—"}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-neutral-600">Spec</dt>
                <dd className="mt-1">
                  {character.spec || "—"} {character.specVersion ? `(v${character.specVersion})` : ""}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-neutral-600">Tags</dt>
                <dd className="mt-2 flex flex-wrap gap-2">
                  {character.tags.length ? (
                    character.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center rounded-full bg-neutral-100 px-3 py-1 text-xs text-neutral-600"
                      >
                        {tag}
                      </span>
                    ))
                  ) : (
                    <span>—</span>
                  )}
                </dd>
              </div>
            </dl>
          </div>

          <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
            <h2 className="text-lg font-semibold text-neutral-900">Greetings & Prompts</h2>
            <div className="mt-4 space-y-4 text-sm text-neutral-700">
              <div>
                <h3 className="font-medium text-neutral-600">First Message</h3>
                <p className="mt-1 whitespace-pre-wrap">{character.firstMessage || "—"}</p>
              </div>
              <div>
                <h3 className="font-medium text-neutral-600">Alternate Greetings</h3>
                {character.alternateGreetings.length ? (
                  <ul className="mt-1 space-y-2">
                    {character.alternateGreetings.map((greeting, index) => (
                      <li key={`${greeting}-${index}`} className="rounded bg-neutral-100 p-3">
                        {greeting}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="mt-1">—</p>
                )}
              </div>
              <div>
                <h3 className="font-medium text-neutral-600">Group-only Greetings</h3>
                {character.groupOnlyGreetings.length ? (
                  <ul className="mt-1 space-y-2">
                    {character.groupOnlyGreetings.map((greeting, index) => (
                      <li key={`${greeting}-${index}`} className="rounded bg-neutral-100 p-3">
                        {greeting}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="mt-1">—</p>
                )}
              </div>
              <div>
                <h3 className="font-medium text-neutral-600">Post-History Instructions</h3>
                <p className="mt-1 whitespace-pre-wrap">
                  {character.postHistoryInstructions || "—"}
                </p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
            <h2 className="text-lg font-semibold text-neutral-900">Extensions</h2>
            <pre className="mt-4 max-h-96 overflow-auto rounded bg-neutral-900 p-4 text-xs text-neutral-100">
              {JSON.stringify(character.extensions ?? {}, null, 2)}
            </pre>
          </div>
        </div>

        <aside className="space-y-6">
          <div className="rounded-lg border border-neutral-200 bg-white p-6 text-center shadow-sm">
            <div className="mx-auto flex h-24 w-24 items-center justify-center overflow-hidden rounded-full bg-neutral-100 text-3xl font-semibold text-neutral-700">
              {character.avatarUrl ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={character.avatarUrl}
                  alt={`${character.name} avatar`}
                  className="h-full w-full object-cover"
                  referrerPolicy="no-referrer"
                />
              ) : (
                character.name.charAt(0).toUpperCase()
              )}
            </div>
            <p className="mt-4 text-xs text-neutral-500">
              Updated {toIsoString(character.updatedAt)}
            </p>
          </div>

          <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
            <h2 className="text-lg font-semibold text-neutral-900">Character Book</h2>
            {sortedBookEntries.length ? (
              <div className="mt-4 space-y-3">
                <p className="text-sm text-neutral-600">
                  <span className="font-medium">Name:</span> {bookName}
                </p>
                <div className="space-y-4">
                  {sortedBookEntries.map((entry) => (
                    <details
                      key={entry.id}
                      className="rounded border border-neutral-200 bg-neutral-50 p-4 text-sm text-neutral-800"
                    >
                      <summary className="cursor-pointer font-semibold text-neutral-700">
                        #{entry.entryId} {entry.name || "Untitled Entry"}
                      </summary>
                      <div className="mt-2 space-y-2">
                        {entry.comment ? (
                          <p className="text-xs text-neutral-500">Comment: {entry.comment}</p>
                        ) : null}
                        {entry.keys?.length ? (
                          <p className="text-xs text-neutral-500">
                            Keys: {entry.keys.join(", ")}
                          </p>
                        ) : null}
                        {entry.secondaryKeys?.length ? (
                          <p className="text-xs text-neutral-500">
                            Secondary Keys: {entry.secondaryKeys.join(", ")}
                          </p>
                        ) : null}
                        <p className="whitespace-pre-wrap text-sm text-neutral-800">
                          {entry.content}
                        </p>
                      </div>
                    </details>
                  ))}
                </div>
              </div>
            ) : (
              <p className="mt-4 text-sm text-neutral-600">No character book entries found.</p>
            )}
          </div>
        </aside>
      </section>
    </main>
  );
}
