import Link from "next/link";
import { notFound, redirect } from "next/navigation";

import ChatPanel from "./ChatPanel";
import type { ChatMessage, ChatSession, LLMModel, Prisma } from "@/generated/prisma";
import { getCharacterForRead } from "@/lib/permissions";
import { prisma } from "@/lib/prisma";
import { auth } from "../../../../../auth";

type SessionWithMessages = ChatSession & {
  messages: ChatMessage[];
  model: Pick<LLMModel, "id" | "displayName"> | null;
};

type ChatMessageForClient = {
  id: string;
  role: "assistant" | "user";
  content: string;
  createdAt: string;
};

type ChatSessionForClient = {
  id: string;
  messages: ChatMessageForClient[];
  model: {
    id: string;
    displayName: string;
  } | null;
};

type SessionSummary = {
  id: string;
  createdAt: string;
  updatedAt: string;
  model: {
    id: string;
    displayName: string;
  } | null;
  messageCount: number;
  lastMessagePreview: string | null;
};

const mapRole = (role: string): "assistant" | "user" =>
  role === "ASSISTANT" ? "assistant" : "user";

const toClientMessages = (messages: ChatMessage[]): ChatMessageForClient[] =>
  messages
    .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
    .map((message) => ({
      id: message.id,
      role: mapRole(message.role),
      content: message.content,
      createdAt: message.createdAt.toISOString(),
    }));

const toClientChat = (session: SessionWithMessages | null): ChatSessionForClient | null => {
  if (!session) {
    return null;
  }

  return {
    id: session.id,
    messages: toClientMessages(session.messages),
    model: session.model
      ? { id: session.model.id, displayName: session.model.displayName }
      : null,
  };
};

const toSessionSummaries = (sessions: SessionWithMessages[]): SessionSummary[] =>
  sessions.map((session) => {
    const messages = toClientMessages(session.messages);
    return {
      id: session.id,
      createdAt: session.createdAt.toISOString(),
      updatedAt: session.updatedAt.toISOString(),
      model: session.model
        ? { id: session.model.id, displayName: session.model.displayName }
        : null,
      messageCount: messages.length,
      lastMessagePreview: messages.length
        ? messages[messages.length - 1]?.content ?? null
        : null,
    };
  });

type ChatModelOption = {
  id: string;
  identifier: string;
  displayName: string;
  providerName: string;
  currency: string;
  promptPricePer1MTokens: number | null;
  completionPricePer1MTokens: number | null;
  isDefault: boolean;
};

type CharacterChatPageParams = {
  params: Promise<{
    slug: string;
  }>;
};

export default async function CharacterChatPage({ params }: CharacterChatPageParams) {
  const { slug } = await params;

  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  const character = await getCharacterForRead({
    slug,
    include: {
      bookEntries: true,
    },
  });

  if (!character) {
    notFound();
  }

  const [latestSession, sessions] = await prisma.$transaction([
    prisma.chatSession.findFirst({
      where: { characterId: character.id },
      orderBy: { updatedAt: "desc" },
      include: {
        messages: true,
        model: {
          select: {
            id: true,
            displayName: true,
          },
        },
      },
    }),
    prisma.chatSession.findMany({
      where: { characterId: character.id },
      orderBy: { updatedAt: "desc" },
      include: {
        messages: true,
        model: {
          select: {
            id: true,
            displayName: true,
          },
        },
      },
    }),
  ]);

  const initialChat = toClientChat(latestSession);
  const sessionSummaries = toSessionSummaries(sessions);
  const includeBookByDefault = character.bookEntries.length > 0;

  const models = await prisma.lLMModel.findMany({
    orderBy: [
      { isDefault: "desc" },
      { displayName: "asc" },
    ],
    include: {
      provider: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  const decimalToNumber = (value: Prisma.Decimal | null): number | null =>
    value ? value.toNumber() : null;

  const modelOptions: ChatModelOption[] = models.map((model) => ({
    id: model.id,
    identifier: model.identifier,
    displayName: model.displayName,
    providerName: model.provider.name,
    currency: model.currency,
    promptPricePer1MTokens: decimalToNumber(model.promptPricePer1MTokens),
    completionPricePer1MTokens: decimalToNumber(model.completionPricePer1MTokens),
    isDefault: model.isDefault,
  }));

  const initialModelId =
    initialChat?.model?.id ??
    modelOptions.find((model) => model.isDefault)?.id ??
    modelOptions[0]?.id ??
    null;

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-5xl flex-col gap-8 px-6 py-12">
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-semibold">Chat: {character.name}</h1>
          <p className="text-sm text-neutral-600">
            Manage ongoing conversations and optionally include the character book in prompts.
          </p>
        </div>
        <Link
          href={`/characters/${character.slug}`}
          className="text-sm font-medium text-neutral-600 transition hover:text-neutral-900"
        >
          ← Back to Character
        </Link>
      </div>

      <ChatPanel
        character={{
          id: character.id,
          slug: character.slug,
          name: character.name,
          characterVersion: character.characterVersion,
          avatarUrl: character.avatarUrl,
          firstMessage: character.firstMessage,
          alternateGreetings: character.alternateGreetings,
          description: character.description,
          scenario: character.scenario,
          tags: character.tags,
        }}
        initialChat={initialChat}
        initialSessions={sessionSummaries}
        initialIncludeBook={includeBookByDefault}
        models={modelOptions}
        initialModelId={initialModelId}
      />
    </main>
  );
}
