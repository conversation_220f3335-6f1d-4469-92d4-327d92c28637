import Link from "next/link";
import { redirect } from "next/navigation";

import { prisma } from "@/lib/prisma";
import { auth } from "../../../auth";

export const dynamic = "force-dynamic";

export default async function CharactersPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  const characters = await prisma.character.findMany({
    orderBy: { createdAt: "desc" },
    select: {
      id: true,
      name: true,
      slug: true,
      avatarUrl: true,
      description: true,
      characterVersion: true,
      creator: true,
    },
  });

  if (characters.length === 0) {
    return (
      <main className="mx-auto flex min-h-screen w-full max-w-3xl flex-col gap-8 px-6 py-12">
        <section className="space-y-4">
          <h1 className="text-3xl font-semibold">Characters</h1>
          <p className="text-sm text-neutral-600">
            Import a character JSON card to see it appear here.
          </p>
        </section>

        <div className="rounded border border-neutral-200 bg-white p-6 shadow-sm">
          <p className="text-sm text-neutral-600">
            Nothing here yet. Head over to the importer and upload your first
            character.
          </p>
          <Link
            href="/characters/import"
            className="mt-4 inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-medium text-white"
          >
            Import a Character
          </Link>
        </div>
      </main>
    );
  }

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-4xl flex-col gap-10 px-6 py-12">
      <section className="space-y-4">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-semibold">Characters</h1>
            <p className="text-sm text-neutral-600">
              Pick a character to jump into a conversation.
            </p>
          </div>
          <Link
            href="/characters/import"
            className="inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-medium text-white"
          >
            Import More
          </Link>
        </div>
      </section>

      <ul className="grid gap-6 md:grid-cols-2">
        {characters.map((character) => (
          <li
            key={character.id}
            className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm transition hover:shadow-md"
          >
            <Link href={`/characters/${character.slug}`} className="block space-y-4">
              <div className="flex items-center gap-3">
                <div className="flex h-12 w-12 items-center justify-center overflow-hidden rounded-full bg-neutral-100 text-lg font-semibold text-neutral-700">
                  {character.avatarUrl ? (
                    // eslint-disable-next-line @next/next/no-img-element
                    <img
                      src={character.avatarUrl}
                      alt={`${character.name} avatar`}
                      className="h-full w-full object-cover"
                      referrerPolicy="no-referrer"
                    />
                  ) : (
                    character.name.charAt(0).toUpperCase()
                  )}
                </div>
                <div>
                  <p className="text-base font-semibold text-neutral-900">
                    {character.name}
                  </p>
                  <p className="text-xs text-neutral-500">
                    Version {character.characterVersion}
                    {character.creator ? ` · by ${character.creator}` : ""}
                  </p>
                </div>
              </div>
              {character.description ? (
                <p className="line-clamp-3 text-sm text-neutral-600">
                  {character.description}
                </p>
              ) : null}
            </Link>
          </li>
        ))}
      </ul>
    </main>
  );
}
