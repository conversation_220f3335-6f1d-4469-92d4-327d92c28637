import { NextResponse } from "next/server";

import type { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";
import { auth } from "../../../../../auth";

const serializeProvider = (
  provider: {
    id: string;
    name: string;
    baseUrl: string | null;
    apiKey: string | null;
    metadata: Prisma.JsonValue | null;
    createdAt: Date;
    updatedAt: Date;
    models: Array<{
      id: string;
      identifier: string;
      displayName: string;
      description: string | null;
    promptPricePer1MTokens: Prisma.Decimal | null;
    completionPricePer1MTokens: Prisma.Decimal | null;
      currency: string;
      isDefault: boolean;
      createdAt: Date;
      updatedAt: Date;
    }>;
  },
) => ({
  id: provider.id,
  name: provider.name,
  baseUrl: provider.baseUrl,
  apiKey: provider.apiKey,
  metadata: provider.metadata,
  createdAt: provider.createdAt.toISOString(),
  updatedAt: provider.updatedAt.toISOString(),
  models: provider.models.map((model) => ({
    id: model.id,
    identifier: model.identifier,
    displayName: model.displayName,
    description: model.description,
    promptPricePer1MTokens: model.promptPricePer1MTokens
      ? model.promptPricePer1MTokens.toNumber()
      : null,
    completionPricePer1MTokens: model.completionPricePer1MTokens
      ? model.completionPricePer1MTokens.toNumber()
      : null,
    currency: model.currency,
    isDefault: model.isDefault,
    createdAt: model.createdAt.toISOString(),
    updatedAt: model.updatedAt.toISOString(),
  })),
});

export async function GET() {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  const providers = await prisma.lLMProvider.findMany({
    orderBy: { name: "asc" },
    include: {
      models: {
        orderBy: [{ isDefault: "desc" }, { displayName: "asc" }],
      },
    },
  });

  return NextResponse.json({ providers: providers.map(serializeProvider) });
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  let payload: unknown;
  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Body must be valid JSON." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Body must be an object." }, { status: 400 });
  }

  const { name, baseUrl, apiKey, metadata } = payload as {
    name?: unknown;
    baseUrl?: unknown;
    apiKey?: unknown;
    metadata?: unknown;
  };

  if (typeof name !== "string" || name.trim().length === 0) {
    return NextResponse.json({ error: "Provider name is required." }, { status: 400 });
  }

  let parsedMetadata: Prisma.JsonValue | null = null;
  if (typeof metadata !== "undefined") {
    if (typeof metadata === "string" && metadata.trim().length > 0) {
      try {
        parsedMetadata = JSON.parse(metadata) as Prisma.JsonValue;
      } catch {
        return NextResponse.json({ error: "Metadata must be valid JSON." }, { status: 400 });
      }
    } else if (metadata === null) {
      parsedMetadata = null;
    } else if (typeof metadata === "object") {
      parsedMetadata = metadata as Prisma.JsonValue;
    } else {
      return NextResponse.json({ error: "Metadata must be JSON." }, { status: 400 });
    }
  }

  try {
    const provider = await prisma.lLMProvider.create({
      data: {
        name: name.trim(),
        baseUrl:
          typeof baseUrl === "string" && baseUrl.trim().length > 0
            ? baseUrl.trim()
            : null,
        apiKey:
          typeof apiKey === "string" && apiKey.trim().length > 0 ? apiKey.trim() : null,
        metadata: parsedMetadata,
      },
      include: {
        models: {
          orderBy: [{ isDefault: "desc" }, { displayName: "asc" }],
        },
      },
    });

    return NextResponse.json({ provider: serializeProvider(provider) });
  } catch (error) {
    console.error("Failed to create LLM provider", error);
    return NextResponse.json({ error: "Failed to create provider." }, { status: 500 });
  }
}
