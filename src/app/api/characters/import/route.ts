import { NextRequest, NextResponse } from "next/server";

import { Prisma } from "@/generated/prisma";
import { parseCharacterCardFile } from "@/lib/characterCard";
import { prisma } from "@/lib/prisma";
import { auth } from "../../../../../auth";

const DEFAULT_VERSION = "main";

const errorResponse = (message: string, status = 400) =>
  NextResponse.json({ error: message }, { status });

const slugify = (name: string, version: string) =>
  `${name}-${version}`
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)+/g, "");

const ensureUniqueSlug = async (
  tx: Prisma.TransactionClient,
  baseSlug: string,
  existingCharacterId: string | null,
) => {
  let slug = baseSlug;
  let counter = 1;

  while (true) {
    const conflict = await tx.character.findUnique({
      where: { slug },
      select: { id: true },
    });

    if (!conflict || (existingCharacterId && conflict.id === existingCharacterId)) {
      return slug;
    }

    slug = `${baseSlug}-${counter}`;
    counter += 1;
  }
};

export async function POST(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    return errorResponse("Not authenticated.", 401);
  }

  const userId = session.user.id;
  const isAdmin = session.user.role === "ADMIN";

  const formData = await req.formData();
  const file = formData.get("file");

  if (!file || !(file instanceof File)) {
    return errorResponse("Upload a JSON or PNG character file as 'file'.");
  }

  let parsed: Record<string, unknown>;

  try {
    const card = await parseCharacterCardFile(file);

    if (!card || typeof card !== "object") {
      return errorResponse("Character file is missing required fields.");
    }

    parsed = card;
  } catch (parseError) {
    const message =
      parseError instanceof Error
        ? parseError.message
        : "Failed to parse character file.";
    return errorResponse(message);
  }

  const data = parsed.data as Record<string, unknown> | undefined;

  if (!data) {
    return errorResponse("Character payload missing 'data' section.");
  }

  const name = typeof data.name === "string" ? data.name.trim() : "";

  if (!name) {
    return errorResponse("Character name is required.");
  }

  const characterVersionRaw =
    typeof data.character_version === "string"
      ? data.character_version.trim()
      : undefined;
  const characterVersion = characterVersionRaw || DEFAULT_VERSION;
  const slug = slugify(name, characterVersion);

  const creator =
    typeof data.creator === "string" ? data.creator.trim() || null : null;
  const avatarFromData =
    typeof data.avatar === "string" ? data.avatar.trim() : null;
  const avatarFromRoot =
    typeof parsed.avatar === "string" ? parsed.avatar.trim() : null;
  const avatarUrl = avatarFromData || avatarFromRoot || null;
  const description =
    typeof data.description === "string" ? data.description : null;
  const personality =
    typeof data.personality === "string" ? data.personality : null;
  const scenario = typeof data.scenario === "string" ? data.scenario : null;
  const firstMessage =
    typeof data.first_mes === "string" ? data.first_mes : null;
  const creatorNotes =
    typeof data.creator_notes === "string" ? data.creator_notes : null;

  const spec = typeof parsed.spec === "string" ? parsed.spec : null;
  const specVersion =
    typeof parsed.spec_version === "string" ? parsed.spec_version : null;

  const firstMessageExample =
    typeof data.mes_example === "string" ? data.mes_example : null;
  const systemPrompt =
    typeof data.system_prompt === "string" ? data.system_prompt : null;

  const alternateGreetings = Array.isArray(data.alternate_greetings)
    ? (data.alternate_greetings as unknown[])
        .map((greeting) =>
          typeof greeting === "string" ? greeting.trim() : undefined,
        )
        .filter((greeting): greeting is string => Boolean(greeting?.length))
    : [];

  const tags = Array.isArray(data.tags)
    ? (data.tags as unknown[])
        .map((tag) => (typeof tag === "string" ? tag.trim() : undefined))
        .filter((tag): tag is string => Boolean(tag?.length))
    : [];

  const groupOnlyGreetings = Array.isArray(data.group_only_greetings)
    ? (data.group_only_greetings as unknown[])
        .map((greeting) =>
          typeof greeting === "string" ? greeting.trim() : undefined,
        )
        .filter((greeting): greeting is string => Boolean(greeting?.length))
    : [];

  const extensions =
    typeof data.extensions === "object" && data.extensions !== null
      ? (data.extensions as Prisma.InputJsonValue)
      : null;

  const postHistoryInstructions =
    typeof data.post_history_instructions === "string"
      ? data.post_history_instructions
      : null;

  const characterBookRaw =
    typeof data.character_book === "object" && data.character_book !== null
      ? (data.character_book as Record<string, unknown>)
      : null;

  const characterBookEntries = Array.isArray(characterBookRaw?.entries)
    ? (characterBookRaw?.entries as unknown[])
        .map((entry, index) => {
          if (!entry || typeof entry !== "object") {
            return null;
          }

          const entryRecord = entry as Record<string, unknown>;
          const entryIdRaw = entryRecord.id;
          const entryId =
            typeof entryIdRaw === "number"
              ? entryIdRaw
              : Number.isFinite(Number(entryIdRaw))
                ? Number(entryIdRaw)
                : index + 1;

          const content = entryRecord.content;

          if (typeof content !== "string" || !content.trim()) {
            return null;
          }

          const keys = Array.isArray(entryRecord.keys)
            ? (entryRecord.keys as unknown[])
                .map((key) => (typeof key === "string" ? key : undefined))
                .filter((key): key is string => Boolean(key?.length))
            : [];

          const secondaryKeys = Array.isArray(entryRecord.secondary_keys)
            ? (entryRecord.secondary_keys as unknown[])
                .map((key) => (typeof key === "string" ? key : undefined))
                .filter((key): key is string => Boolean(key?.length))
            : [];

          const extensionsValue =
            typeof entryRecord.extensions === "object" && entryRecord.extensions !== null
              ? (entryRecord.extensions as Prisma.InputJsonValue)
              : null;

          return {
            entryId,
            name:
              typeof entryRecord.name === "string" ? entryRecord.name : null,
            comment:
              typeof entryRecord.comment === "string" ? entryRecord.comment : null,
            content,
            keys,
            secondaryKeys,
            position:
              typeof entryRecord.position === "string"
                ? entryRecord.position
                : null,
            constant:
              typeof entryRecord.constant === "boolean"
                ? entryRecord.constant
                : null,
            selective:
              typeof entryRecord.selective === "boolean"
                ? entryRecord.selective
                : null,
            insertionOrder:
              typeof entryRecord.insertion_order === "number"
                ? entryRecord.insertion_order
                : null,
            enabled:
              typeof entryRecord.enabled === "boolean"
                ? entryRecord.enabled
                : null,
            extensions: extensionsValue,
          };
        })
        .filter((entry): entry is {
          entryId: number;
          name: string | null;
          comment: string | null;
          content: string;
          keys: string[];
          secondaryKeys: string[];
          position: string | null;
          constant: boolean | null;
          selective: boolean | null;
          insertionOrder: number | null;
          enabled: boolean | null;
          extensions: Prisma.InputJsonValue | null;
        } => Boolean(entry))
    : [];

  try {
    const result = await prisma.$transaction(async (tx) => {
      const existingCharacter = await tx.character.findUnique({
        where: {
          name_characterVersion: {
            name,
            characterVersion,
          },
        },
        select: {
          id: true,
          ownerId: true,
        },
      });

      if (
        existingCharacter &&
        existingCharacter.ownerId &&
        existingCharacter.ownerId !== userId &&
        !isAdmin
      ) {
        throw new Error("FORBIDDEN_CHARACTER_UPDATE");
      }

      const uniqueSlug = await ensureUniqueSlug(
        tx,
        slug,
        existingCharacter?.id ?? null,
      );

      const baseData = {
        slug: uniqueSlug,
        creator,
        avatarUrl,
        description,
        personality,
        scenario,
        firstMessage,
        firstMessageExample,
        systemPrompt,
        alternateGreetings,
        groupOnlyGreetings,
        tags,
        creatorNotes,
        spec,
        specVersion,
        postHistoryInstructions,
        characterBook: characterBookRaw as Prisma.InputJsonValue | null,
        extensions,
        rawData: parsed as Prisma.InputJsonValue,
        ownerId: existingCharacter?.ownerId ?? userId,
      };

      let characterId: string;
      let characterResult: {
        id: string;
        slug: string;
        name: string;
        characterVersion: string;
      };

      if (existingCharacter) {
        characterResult = await tx.character.update({
          where: { id: existingCharacter.id },
          data: baseData,
          select: {
            id: true,
            slug: true,
            name: true,
            characterVersion: true,
          },
        });
        characterId = existingCharacter.id;
      } else {
        characterResult = await tx.character.create({
          data: {
            name,
            characterVersion,
            ...baseData,
          },
          select: {
            id: true,
            slug: true,
            name: true,
            characterVersion: true,
          },
        });
        characterId = characterResult.id;
      }

      await tx.characterBookEntry.deleteMany({
        where: { characterId },
      });

      if (characterBookEntries.length > 0) {
        await tx.characterBookEntry.createMany({
          data: characterBookEntries.map((entry) => ({
            characterId,
            entryId: entry.entryId,
            name: entry.name,
            comment: entry.comment,
            content: entry.content,
            keys: entry.keys,
            secondaryKeys: entry.secondaryKeys,
            position: entry.position,
            constant: entry.constant,
            selective: entry.selective,
            insertionOrder: entry.insertionOrder,
            enabled: entry.enabled,
            extensions: entry.extensions,
          })),
        });
      }

      return characterResult;
    });

    return NextResponse.json({
      character: result,
    });
  } catch (error) {
    if (error instanceof Error && error.message === "FORBIDDEN_CHARACTER_UPDATE") {
      return errorResponse("You do not have permission to modify this character.", 403);
    }
    console.error("Character import failed", error);
    return errorResponse("Failed to save character.", 500);
  }
}
