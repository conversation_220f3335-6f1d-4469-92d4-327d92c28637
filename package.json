{"name": "oa-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint src --ignore-pattern 'src/generated/**' --ignore-pattern 'SillyTavern/**'"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.16.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "next": "15.5.3", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "openai": "^4.77.0", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "prisma": "^6.16.2", "tailwindcss": "^4", "tsx": "^4.20.5", "tw-animate-css": "^1.3.8", "typescript": "^5"}}